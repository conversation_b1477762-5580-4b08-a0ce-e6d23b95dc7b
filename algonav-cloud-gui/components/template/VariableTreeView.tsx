import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  CircularProgress,
  Alert,
  Paper,
  Grid,
  Divider
} from '@mui/material';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import { TreeItem } from '@mui/x-tree-view/TreeItem';
import {
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useVariableTree } from '@/lib/hooks/useVariableTree';
import { VariableStatusBadge } from './VariableStatusBadge';
import { VariableTreeNode } from '@/app/api/variable-tree/route';

interface VariableTreeViewProps {
  templateId: number;
  onVariableSelect?: (variableName: string) => void;
}

interface TreeItemData {
  id: string;
  label: string;
  type: 'category' | 'dataset';
  variables?: any[];
  children?: TreeItemData[];
}

// Build hierarchical tree structure from flat data
const buildHierarchicalTree = (nodes: VariableTreeNode[]): VariableTreeNode[] => {
  const nodeMap = new Map<number, VariableTreeNode & { children: VariableTreeNode[] }>();
  const rootNodes: VariableTreeNode[] = [];
  
  // First pass: create all nodes with empty children arrays
  nodes.forEach(node => {
    nodeMap.set(node.id, { ...node, children: [] });
  });
  
  // Second pass: build parent-child relationships
  nodes.forEach(node => {
    const nodeWithChildren = nodeMap.get(node.id)!;
    
    // Add datasets as children
    if (node.datasets) {
      node.datasets.forEach(dataset => {
        nodeWithChildren.children.push(dataset);
      });
    }
    
    // For categories, check if they have a parent
    if (node.type === 'category') {
      // Find parent by checking level - this is a simplified approach
      // In a real scenario, you'd have parent_id information
      const hasParent = nodes.some(otherNode => 
        otherNode.type === 'category' && 
        otherNode.level !== undefined && 
        node.level !== undefined &&
        otherNode.level < node.level
      );
      
      if (!hasParent || node.level === 0) {
        rootNodes.push(nodeWithChildren);
      }
    }
  });
  
  return rootNodes;
};

// Convert tree data to MUI X TreeView format (only categories and datasets, no variables)
const convertToTreeItems = (
  nodes: VariableTreeNode[]
): TreeItemData[] => {
  return nodes.map((node) => {
    const nodeId = `${node.type}-${node.id}`;
    const children: TreeItemData[] = [];
    
    // Add child nodes (both categories and datasets)
    if (node.children && node.children.length > 0) {
      children.push(...convertToTreeItems(node.children));
    }
    
    const nodeLabel = node.description 
      ? `${node.name} (${node.description})` 
      : node.name;
    
    return {
      id: nodeId,
      label: nodeLabel,
      type: node.type as 'category' | 'dataset',
      variables: node.variables,
      children: children.length > 0 ? children : undefined
    };
  });
};

export const VariableTreeView: React.FC<VariableTreeViewProps> = ({
  templateId,
  onVariableSelect
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string | null>(null);
  const [selectedNode, setSelectedNode] = useState<TreeItemData | null>(null);
  const [treeItems, setTreeItems] = useState<TreeItemData[]>([]);
  
  const { data, loading, error, refetch, getVariableState } = useVariableTree({
    templateId,
    enabled: true
  });

  // Convert tree data and store in state
  React.useEffect(() => {
    if (data?.tree) {
      const hierarchicalTree = buildHierarchicalTree(data.tree);
      setTreeItems(convertToTreeItems(hierarchicalTree));
    }
  }, [data]);

  // Find selected node when selection changes
  React.useEffect(() => {
    if (selectedItems && treeItems.length > 0) {
      const findNode = (items: TreeItemData[], id: string): TreeItemData | null => {
        for (const item of items) {
          if (item.id === id) return item;
          if (item.children) {
            const found = findNode(item.children, id);
            if (found) return found;
          }
        }
        return null;
      };
      setSelectedNode(findNode(treeItems, selectedItems));
    } else {
      setSelectedNode(null);
    }
  }, [selectedItems, treeItems]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Fehler beim Laden der Variablen: {error}
        <Box sx={{ mt: 1 }}>
          <Chip 
            label="Erneut versuchen" 
            onClick={refetch} 
            size="small" 
            variant="outlined"
          />
        </Box>
      </Alert>
    );
  }

  if (!data) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        Keine Daten verfügbar
      </Alert>
    );
  }

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Typography variant="h6" gutterBottom>
        Variablen-Baum (Template ID: {data.template_id})
      </Typography>

      {/* Template Variables */}
      {data.template_variables.length > 0 && (
        <Accordion defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              Template-Variablen ({data.template_variables.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {data.template_variables.map((variable, index) => {
                const state = getVariableState(variable.variable_name);
                return (
                  <VariableStatusBadge
                    key={index}
                    variableName={variable.variable_name}
                    primaryState={state.primaryState}
                    secondaryState={state.secondaryState}
                    counts={state.counts}
                  />
                );
              })}
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Two-column layout */}
      <Grid container spacing={2} sx={{ height: 'calc(100% - 200px)' }}>
        {/* Left column: Tree */}
        <Grid item xs={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Kategorie-Hierarchie
            </Typography>
            {data.tree.length > 0 ? (
              <RichTreeView
                items={treeItems}
                expandedItems={expandedItems}
                onExpandedItemsChange={(event, itemIds) => setExpandedItems(itemIds)}
                selectedItems={selectedItems}
                onSelectedItemsChange={(event, itemId) => setSelectedItems(itemId)}
                sx={{ 
                  flexGrow: 1, 
                  maxWidth: '100%', 
                  overflowY: 'auto',
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  p: 1
                }}
              />
            ) : (
              <Alert severity="info">
                Keine Kategorien gefunden
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Right column: Variables */}
        <Grid item xs={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Variablen
            </Typography>
            {selectedNode ? (
              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedNode.type === 'dataset' ? 'Dataset' : 'Kategorie'}: {selectedNode.label}
                </Typography>
                {selectedNode.variables && selectedNode.variables.length > 0 ? (
                  <Box sx={{ 
                    bgcolor: 'grey.50', 
                    p: 2, 
                    borderRadius: 1, 
                    maxHeight: '400px', 
                    overflowY: 'auto',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }}>
                    <pre>{JSON.stringify(selectedNode.variables, null, 2)}</pre>
                  </Box>
                ) : (
                  <Alert severity="info">
                    Keine Variablen in diesem {selectedNode.type === 'dataset' ? 'Dataset' : 'dieser Kategorie'}
                  </Alert>
                )}
              </Box>
            ) : (
              <Alert severity="info">
                Wählen Sie ein Dataset oder eine Kategorie aus dem Baum aus, um die Variablen anzuzeigen
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default VariableTreeView;