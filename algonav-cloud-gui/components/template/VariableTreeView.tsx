import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  CircularProgress,
  Alert,
  Paper,
  Grid
} from '@mui/material';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import {
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useVariableTree } from '@/lib/hooks/useVariableTree';
import { VariableStatusBadge } from './VariableStatusBadge';
import { api } from '@/lib/services/api';

interface VariableTreeViewProps {
  templateId: number;
  onVariableSelect?: (variableName: string) => void;
}

interface TreeItemData {
  id: string;
  label: string;
  type: 'category' | 'dataset';
  variables?: any[];
  children?: TreeItemData[];
}

interface TreeNode {
  id: number;
  parentId: number | null;
  name: string;
  path: string;
  level: number;
  datasetCnt?: number;
}

interface Dataset {
  id: number;
  name: string;
  description?: string;
  category_id: number;
}

// Build tree structure directly from variable tree data
const buildTreeFromVariableData = (variableTreeNodes: any[]): TreeItemData[] => {
  const treeItems: TreeItemData[] = [];

  variableTreeNodes.forEach(node => {
    const categoryItem: TreeItemData = {
      id: `category-${node.id}`,
      label: node.description ? `${node.name} (${node.description})` : node.name,
      type: 'category',
      variables: node.variables || [],
      children: []
    };

    // Add datasets as children if they exist
    if (node.datasets && Array.isArray(node.datasets)) {
      node.datasets.forEach((dataset: any) => {
        const datasetItem: TreeItemData = {
          id: `dataset-${dataset.id}`,
          label: dataset.description ? `${dataset.name} (${dataset.description})` : dataset.name,
          type: 'dataset',
          variables: dataset.variables || []
        };
        categoryItem.children!.push(datasetItem);
      });
    }

    treeItems.push(categoryItem);
  });

  return treeItems;
};

// Helper function to sort tree items recursively
const sortTreeItems = (items: TreeItemData[]): TreeItemData[] => {
  return items
    .sort((a, b) => a.label.localeCompare(b.label))
    .map(item => ({
      ...item,
      children: item.children ? sortTreeItems(item.children) : undefined
    }));
};

export const VariableTreeView: React.FC<VariableTreeViewProps> = ({
  templateId,
  onVariableSelect
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<string | null>(null);
  const [selectedNode, setSelectedNode] = useState<TreeItemData | null>(null);
  const [treeItems, setTreeItems] = useState<TreeItemData[]>([]);
  const [treeLoading, setTreeLoading] = useState(false);
  const [treeError, setTreeError] = useState<string | null>(null);

  const { data, loading, error, refetch, getVariableState } = useVariableTree({
    templateId,
    enabled: true
  });

  // Build tree from variable tree data when it's available
  useEffect(() => {
    if (data?.tree) {
      setTreeLoading(true);
      try {
        // Build tree directly from variable tree data
        const hierarchicalTree = buildTreeFromVariableData(data.tree);
        setTreeItems(sortTreeItems(hierarchicalTree));
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setTreeError(errorMessage);
        console.error('Error building tree:', err);
      } finally {
        setTreeLoading(false);
      }
    }
  }, [data]);

  // Find selected node when selection changes
  React.useEffect(() => {
    if (selectedItems && treeItems.length > 0) {
      const findNode = (items: TreeItemData[], id: string): TreeItemData | null => {
        for (const item of items) {
          if (item.id === id) return item;
          if (item.children) {
            const found = findNode(item.children, id);
            if (found) return found;
          }
        }
        return null;
      };
      setSelectedNode(findNode(treeItems, selectedItems));
    } else {
      setSelectedNode(null);
    }
  }, [selectedItems, treeItems]);

  if (loading || treeLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || treeError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Fehler beim Laden der Daten: {error || treeError}
        <Box sx={{ mt: 1 }}>
          <Chip
            label="Erneut versuchen"
            onClick={refetch}
            size="small"
            variant="outlined"
          />
        </Box>
      </Alert>
    );
  }

  if (!data && treeItems.length === 0) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        Keine Daten verfügbar
      </Alert>
    );
  }

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Typography variant="h6" gutterBottom>
        Variablen-Baum (Template ID: {templateId})
      </Typography>

      {/* Template Variables */}
      {data && data.template_variables.length > 0 && (
        <Accordion defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              Template-Variablen ({data.template_variables.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {data.template_variables.map((variable, index) => {
                const state = getVariableState(variable.variable_name);
                return (
                  <VariableStatusBadge
                    key={index}
                    variableName={variable.variable_name}
                    primaryState={state.primaryState}
                    secondaryState={state.secondaryState}
                    counts={state.counts}
                  />
                );
              })}
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Two-column layout */}
      <Grid container spacing={2} sx={{ height: 'calc(100% - 200px)' }}>
        {/* Left column: Tree */}
        <Grid item xs={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Kategorie-Hierarchie
            </Typography>
            {treeItems.length > 0 ? (
              <RichTreeView
                items={treeItems}
                expandedItems={expandedItems}
                onExpandedItemsChange={(event, itemIds) => setExpandedItems(itemIds)}
                selectedItems={selectedItems}
                onSelectedItemsChange={(event, itemId) => setSelectedItems(itemId)}
                sx={{
                  flexGrow: 1,
                  maxWidth: '100%',
                  overflowY: 'auto',
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  p: 1
                }}
              />
            ) : (
              <Alert severity="info">
                Keine Kategorien gefunden
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Right column: Variables */}
        <Grid item xs={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Variablen
            </Typography>
            {selectedNode ? (
              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedNode.type === 'dataset' ? 'Dataset' : 'Kategorie'}: {selectedNode.label}
                </Typography>
                {selectedNode.variables && selectedNode.variables.length > 0 ? (
                  <Box sx={{ 
                    bgcolor: 'grey.50', 
                    p: 2, 
                    borderRadius: 1, 
                    maxHeight: '400px', 
                    overflowY: 'auto',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }}>
                    <pre>{JSON.stringify(selectedNode.variables, null, 2)}</pre>
                  </Box>
                ) : (
                  <Alert severity="info">
                    Keine Variablen in diesem {selectedNode.type === 'dataset' ? 'Dataset' : 'dieser Kategorie'}
                  </Alert>
                )}
              </Box>
            ) : (
              <Alert severity="info">
                Wählen Sie ein Dataset oder eine Kategorie aus dem Baum aus, um die Variablen anzuzeigen
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default VariableTreeView;