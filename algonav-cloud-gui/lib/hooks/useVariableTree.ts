import { useState, useEffect, useCallback } from 'react';
import { VariableTreeResponse, VariableWithContext, VariableTreeNode } from '@/app/api/variable-tree/route';

export interface UseVariableTreeOptions {
  templateId: number;
  enabled?: boolean;
  refetchInterval?: number;
}

export interface UseVariableTreeReturn {
  data: VariableTreeResponse | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getVariablesByName: (variableName: string) => VariableWithContext[];
  getVariableState: (variableName: string) => {
    primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
    secondaryState?: 'active' | 'overridden' | 'not-set';
    counts: {
      active: number;
      overridden: number;
      notSet: number;
      definedHigher: number;
      total: number;
    };
  };
}

export function useVariableTree({
  templateId,
  enabled = true,
  refetchInterval
}: UseVariableTreeOptions): UseVariableTreeReturn {
  const [data, setData] = useState<VariableTreeResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchVariableTree = useCallback(async () => {
    if (!enabled || !templateId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/variable-tree?templateId=${templateId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch variable tree');
      }

      const result: VariableTreeResponse = await response.json();
      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching variable tree:', err);
    } finally {
      setLoading(false);
    }
  }, [templateId, enabled]);

  // Initial fetch
  useEffect(() => {
    fetchVariableTree();
  }, [fetchVariableTree]);

  // Optional refetch interval
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchVariableTree, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchVariableTree, refetchInterval, enabled]);

  // Helper function to get all variables with a specific name across the tree
  const getVariablesByName = useCallback((variableName: string): VariableWithContext[] => {
    if (!data) return [];

    const variables: VariableWithContext[] = [];

    // Add template variables
    const templateVar = data.template_variables.find(v => v.variable_name === variableName);
    if (templateVar) {
      variables.push(templateVar);
    }

    // Recursively search through tree nodes
    const searchNode = (node: VariableTreeNode) => {
      // Check node variables
      const nodeVar = node.variables.find(v => v.variable_name === variableName);
      if (nodeVar) {
        variables.push(nodeVar);
      }

      // Check datasets
      if (node.datasets) {
        node.datasets.forEach(dataset => {
          const datasetVar = dataset.variables.find(v => v.variable_name === variableName);
          if (datasetVar) {
            variables.push(datasetVar);
          }
        });
      }

      // Check children
      if (node.children) {
        node.children.forEach(searchNode);
      }
    };

    data.tree.forEach(searchNode);
    return variables;
  }, [data]);

  // Helper function to calculate variable state for mixed state visualization
  const getVariableState = useCallback((variableName: string) => {
    const variables = getVariablesByName(variableName);
    
    const counts = {
      active: 0,
      overridden: 0,
      notSet: 0,
      definedHigher: 0,
      total: variables.length
    };

    variables.forEach(variable => {
      if (variable.source_level === 'Template' || variable.source_level === 'Category') {
        if (variable.is_overridden) {
          counts.overridden++;
        } else {
          counts.definedHigher++;
        }
      } else if (variable.is_active) {
        counts.active++;
      } else {
        counts.notSet++;
      }
    });

    // Determine primary state based on majority
    let primaryState: 'active' | 'overridden' | 'not-set' | 'defined-higher';
    let secondaryState: 'active' | 'overridden' | 'not-set' | undefined;

    const maxCount = Math.max(counts.active, counts.overridden, counts.notSet, counts.definedHigher);
    
    if (counts.active === maxCount) {
      primaryState = 'active';
    } else if (counts.overridden === maxCount) {
      primaryState = 'overridden';
    } else if (counts.definedHigher === maxCount) {
      primaryState = 'defined-higher';
    } else {
      primaryState = 'not-set';
    }

    // Determine secondary state (second most common)
    const sortedCounts = [
      { state: 'active' as const, count: counts.active },
      { state: 'overridden' as const, count: counts.overridden },
      { state: 'not-set' as const, count: counts.notSet }
    ].sort((a, b) => b.count - a.count);

    if (sortedCounts[1].count > 0 && sortedCounts[0].count !== counts.total) {
      secondaryState = sortedCounts[1].state;
    }

    return {
      primaryState,
      secondaryState,
      counts
    };
  }, [getVariablesByName]);

  return {
    data,
    loading,
    error,
    refetch: fetchVariableTree,
    getVariablesByName,
    getVariableState
  };
}